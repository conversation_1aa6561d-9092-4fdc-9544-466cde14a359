import React from 'react';

interface SimpleCelebrationDialogProps {
  isOpen: boolean;
  onClose: () => void;
  selectedImage: string;
  gridSize: number;
  selectedDifficulty: string;
  resetPuzzle: () => void;
  changeImage: () => void;
  isMobile: boolean;
}

const SimpleCelebrationDialog: React.FC<SimpleCelebrationDialogProps> = ({
  isOpen,
  onClose,
  selectedImage,
  gridSize,
  selectedDifficulty,
  resetPuzzle,
  changeImage,
  isMobile
}) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/60 backdrop-blur-sm z-50 animate-fadeIn flex items-center justify-center p-4">
      <div className={`bg-white rounded-2xl shadow-2xl relative animate-scaleIn ${
        isMobile ? 'max-w-sm w-full p-6' : 'max-w-lg w-full p-8'
      }`}>

        {/* Close Button */}
        <button
          onClick={onClose}
          className="absolute top-3 right-3 w-8 h-8 bg-gray-100 hover:bg-gray-200 rounded-full flex items-center justify-center text-gray-500 hover:text-gray-700 transition-colors"
          aria-label="Close popup"
        >
          <span className="text-sm">✕</span>
        </button>

        {/* Content */}
        <div className="text-center space-y-6">
          {/* Trophy */}
          <div className="text-6xl animate-bounce">
            <span className="emoji" style={{fontFamily: 'Apple Color Emoji, Segoe UI Emoji, Noto Color Emoji, sans-serif'}}>🏆</span>
          </div>

          {/* Completed Image */}
          <div className="relative mx-auto">
            <div className={`rounded-xl overflow-hidden shadow-lg ${
              isMobile ? 'w-32 h-32' : 'w-40 h-40'
            }`}>
              <img
                src={selectedImage}
                alt="Completed Puzzle"
                className="w-full h-full object-cover"
              />
            </div>
            {/* Decorative emojis around image */}
            <div className="absolute -top-2 -right-2 text-2xl animate-bounce">
              <span className="emoji" style={{fontFamily: 'Apple Color Emoji, Segoe UI Emoji, Noto Color Emoji, sans-serif'}}>🎉</span>
            </div>
            <div className="absolute -bottom-2 -left-2 text-2xl animate-bounce" style={{animationDelay: '0.3s'}}>
              <span className="emoji" style={{fontFamily: 'Apple Color Emoji, Segoe UI Emoji, Noto Color Emoji, sans-serif'}}>✨</span>
            </div>
          </div>

          {/* Congratulations Text */}
          <div className="space-y-4">
            <h2 className={`font-bold text-purple-600 font-thai ${
              isMobile ? 'text-2xl' : 'text-3xl'
            }`}>
              <span className="emoji" style={{fontFamily: 'Apple Color Emoji, Segoe UI Emoji, Noto Color Emoji, sans-serif'}}>🎉</span> เยี่ยมมาก! เสร็จแล้ว! <span className="emoji" style={{fontFamily: 'Apple Color Emoji, Segoe UI Emoji, Noto Color Emoji, sans-serif'}}>🎉</span>
            </h2>
            
            <p className={`text-pink-600 font-thai ${
              isMobile ? 'text-lg' : 'text-xl'
            }`}>
              ประกอบจิ๊กซอว์ {gridSize}×{gridSize} ชิ้นสำเร็จแล้ว! <span className="emoji" style={{fontFamily: 'Apple Color Emoji, Segoe UI Emoji, Noto Color Emoji, sans-serif'}}>🧩</span>
            </p>

            <div className="flex items-center justify-center gap-2">
              <span className="text-purple-600 font-thai">ระดับความยาก:</span>
              <span className="bg-green-100 text-green-700 px-3 py-1 rounded-full text-sm font-semibold">
                {selectedDifficulty === 'easy' ? <><span className="emoji" style={{fontFamily: 'Apple Color Emoji, Segoe UI Emoji, Noto Color Emoji, sans-serif'}}>😊</span> ง่าย</> : selectedDifficulty === 'medium' ? <><span className="emoji" style={{fontFamily: 'Apple Color Emoji, Segoe UI Emoji, Noto Color Emoji, sans-serif'}}>🤔</span> ปานกลาง</> : <><span className="emoji" style={{fontFamily: 'Apple Color Emoji, Segoe UI Emoji, Noto Color Emoji, sans-serif'}}>😤</span> ยาก</>}
              </span>
            </div>
          </div>

          {/* Action Buttons */}
          <div className={`flex gap-3 ${isMobile ? 'flex-col' : 'flex-row justify-center'}`}>
            <button
              onClick={resetPuzzle}
              className="bg-purple-500 hover:bg-purple-600 text-white px-6 py-3 rounded-lg font-semibold transition-colors font-thai"
            >
              <span className="flex items-center justify-center gap-2">
                <span className="emoji" style={{fontFamily: 'Apple Color Emoji, Segoe UI Emoji, Noto Color Emoji, sans-serif'}}>🔄</span>
                เล่นภาพนี้อีกครั้ง
              </span>
            </button>
            <button
              onClick={changeImage}
              className="bg-green-500 hover:bg-green-600 text-white px-6 py-3 rounded-lg font-semibold transition-colors font-thai"
            >
              <span className="flex items-center justify-center gap-2">
                <span className="emoji" style={{fontFamily: 'Apple Color Emoji, Segoe UI Emoji, Noto Color Emoji, sans-serif'}}>🎮</span>
                ความทรงจำใหม่
              </span>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SimpleCelebrationDialog;
